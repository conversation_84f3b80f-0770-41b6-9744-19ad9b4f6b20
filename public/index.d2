title: Laravel Application Entry Point - public/index.php

# Laravel Application Bootstrap Flow
direction: down

# External Dependencies
composer: {
  label: "Composer Autoloader"
  shape: package
  style.fill: "#e1f5fe"
}

vendor_autoload: {
  label: "vendor/autoload.php"
  shape: document
  style.fill: "#f3e5f5"
}

bootstrap_app: {
  label: "bootstrap/app.php"
  shape: document
  style.fill: "#f3e5f5"
}

# Main Application Components
index_php: {
  label: "public/index.php\n(Entry Point)"
  shape: rectangle
  style.fill: "#fff3e0"
  style.stroke: "#ff9800"
  style.stroke-width: 3
}

laravel_start: {
  label: "LARAVEL_START\n(Performance Timer)"
  shape: oval
  style.fill: "#e8f5e8"
}

app_instance: {
  label: "Laravel Application\nInstance ($app)"
  shape: hexagon
  style.fill: "#e3f2fd"
}

kernel: {
  label: "HTTP Kernel\n(Illuminate\\Contracts\\Http\\Kernel)"
  shape: rectangle
  style.fill: "#fce4ec"
}

request: {
  label: "HTTP Request\n(Illuminate\\Http\\Request)"
  shape: parallelogram
  style.fill: "#f1f8e9"
}

response: {
  label: "HTTP Response"
  shape: parallelogram
  style.fill: "#fff8e1"
}

# Browser/Client
browser: {
  label: "Client Browser"
  shape: cloud
  style.fill: "#e0f2f1"
}

# Flow connections
browser -> index_php: "HTTP Request"

index_php -> laravel_start: "1. Start timer"
index_php -> vendor_autoload: "2. Load autoloader"
vendor_autoload -> composer: "requires"

index_php -> bootstrap_app: "3. Bootstrap app"
bootstrap_app -> app_instance: "returns"

index_php -> kernel: "4. Make kernel"
app_instance -> kernel: "creates"

index_php -> request: "5. Capture request"
kernel -> request: "handles"

kernel -> response: "6. Generate response"
response -> browser: "7. Send response"

index_php -> kernel: "8. Terminate"

# Process Flow Steps
steps: {
  label: "Execution Steps"
  shape: rectangle
  style.fill: "#f5f5f5"
  
  step1: "1. Define LARAVEL_START constant"
  step2: "2. Require Composer autoloader"
  step3: "3. Bootstrap Laravel application"
  step4: "4. Create HTTP Kernel instance"
  step5: "5. Capture incoming request"
  step6: "6. Handle request & generate response"
  step7: "7. Send response to browser"
  step8: "8. Terminate kernel (cleanup)"
  
  step1 -> step2
  step2 -> step3
  step3 -> step4
  step4 -> step5
  step5 -> step6
  step6 -> step7
  step7 -> step8
}

# Key Components Description
components: {
  label: "Key Components"
  shape: rectangle
  style.fill: "#fafafa"
  
  autoloader: {
    label: "Autoloader"
    description: "Composer's PSR-4 class loading"
  }
  
  application: {
    label: "Application"
    description: "Laravel service container & framework core"
  }
  
  http_kernel: {
    label: "HTTP Kernel"
    description: "Handles HTTP requests through middleware pipeline"
  }
  
  request_response: {
    label: "Request/Response"
    description: "HTTP request capture and response generation"
  }
}
