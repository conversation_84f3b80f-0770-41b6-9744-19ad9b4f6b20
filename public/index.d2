title: Laravel Application Entry Point - public/index.php

direction: down

browser: {
  label: "Client Browser"
  shape: cloud
  style.fill: "#e0f2f1"
}

index_php: {
  label: "public/index.php\n(Entry Point)"
  shape: rectangle
  style.fill: "#fff3e0"
  style.stroke: "#ff9800"
  style.stroke-width: 3
}

laravel_start: {
  label: "LARAVEL_START\n(Performance Timer)"
  shape: oval
  style.fill: "#e8f5e8"
}

vendor_autoload: {
  label: "vendor/autoload.php"
  shape: document
  style.fill: "#f3e5f5"
}

composer: {
  label: "Composer Autoloader"
  shape: package
  style.fill: "#e1f5fe"
}


bootstrap_app: {
  label: "bootstrap/app.php"
  shape: document
  style.fill: "#f3e5f5"
}



app_instance: {
  label: "Buxus\\Core\\BuxusApplication"
  shape: hexagon
  style.fill: "#e3f2fd"
}

kernel_instance: {
  label: "App\\Http\\Kernel"
  shape: hexagon
  style.fill: "#e3f2fd"
}

request: {
  label: "Illuminate\\Http\\Request::capture()"
  shape: parallelogram
  style.fill: "#f1f8e9"
}

response: {
  label: "Illuminate\\Http\\Response"
  shape: hexagon
  style.fill: "#e3f2fd"
}

response_send: {
  label: "\$response->send()"
  shape: parallelogram
  style.fill: "#f1f8e9"
}

browser_output: {
  label: "Client Browser"
  shape: cloud
  style.fill: "#e0f2f1"
}

terminate: {
  label: "Terminate"
  shape: rectangle
  style.fill: "#fce4ec"
}

# Flow connections
browser -> index_php: "HTTP Request"
index_php -> laravel_start: "define timer"
index_php -> vendor_autoload: "autoloader"
vendor_autoload -> composer
index_php -> bootstrap_app: "make app"
bootstrap_app -> app_instance: "returns"
index_php -> kernel_instance: "make kernel"
index_php -> request: "handle HTTP request"
request -> response: "returns"
index_php -> response_send: "send response"
response_send -> browser_output: "HTTP response"
index_php -> terminate: "terminate"
